#!/bin/bash
set -e

# Coder TencentCloud Template Packaging Script
# Packages Terraform files at ROOT level of zip (Coder requirement)
# Outputs TencentCloud.zip to /Users/<USER>/Downloads/

echo "🚀 开始打包 Coder TencentCloud 模板..."

# 定义变量
TEMPLATE_NAME="TencentCloud"
DOWNLOADS_DIR="/Users/<USER>/Downloads"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEMPLATE_DIR="$(dirname "$SCRIPT_DIR")"
REQUIRED_FILES=("tencentcloud.tf" "variables.tf")

echo "📁 脚本位置: $SCRIPT_DIR"
echo "📁 模板目录: $TEMPLATE_DIR"

# 检查模板目录
if [ ! -d "$TEMPLATE_DIR" ]; then
    echo "❌ 错误: 模板目录不存在: $TEMPLATE_DIR"
    exit 1
fi

# 检查必需文件
echo "📋 检查必需文件..."
cd "$TEMPLATE_DIR"
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 错误: 缺少必需文件 $file"
        exit 1
    else
        echo "   ✅ $file"
    fi
done

# 检查目标目录
if [ ! -d "$DOWNLOADS_DIR" ]; then
    echo "❌ 错误: Downloads 目录不存在: $DOWNLOADS_DIR"
    exit 1
fi

# 创建临时目录
TEMP_DIR=$(mktemp -d)
echo "📁 创建临时目录: $TEMP_DIR"

# 复制文件到临时目录 (ROOT LEVEL - 这是关键修复!)
echo "📋 复制模板文件到根级别..."
for file in "${REQUIRED_FILES[@]}"; do
    cp "$TEMPLATE_DIR/$file" "$TEMP_DIR/"
    echo "   📄 复制 $file"
done

# 创建 ZIP 压缩包 (文件在根级别)
ARCHIVE_PATH="$DOWNLOADS_DIR/$TEMPLATE_NAME.zip"
echo "📦 创建压缩包: $ARCHIVE_PATH"

cd "$TEMP_DIR"
zip -r "$TEMPLATE_NAME.zip" *.tf

# 移动到目标目录
mv "$TEMPLATE_NAME.zip" "$DOWNLOADS_DIR/"

# 清理临时目录
rm -rf "$TEMP_DIR"

# 验证结果
if [ -f "$ARCHIVE_PATH" ]; then
    echo "✅ 打包完成!"
    echo "📁 压缩包位置: $ARCHIVE_PATH"
    echo "📊 文件大小: $(ls -lh "$ARCHIVE_PATH" | awk '{print $5}')"
    echo ""
    echo "📋 压缩包内容 (根级别结构):"
    unzip -l "$ARCHIVE_PATH"
    echo ""
    echo "🎉 TencentCloud.zip 已准备就绪!"
    echo "💡 文件现在位于ZIP根级别，符合Coder要求"
    echo "🚀 可以上传到 Coder 平台了!"
else
    echo "❌ 错误: 压缩包创建失败"
    exit 1
fi
