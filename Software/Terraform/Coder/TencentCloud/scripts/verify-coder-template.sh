#!/bin/bash
set -e

# Coder Template Verification Script
# Verifies that the TencentCloud.zip meets Coder requirements

DOWNLOADS_DIR="/Users/<USER>/Downloads"
TEMPLATE_ZIP="$DOWNLOADS_DIR/TencentCloud.zip"

echo "🔍 验证 Coder 模板包..."

# Check if zip exists
if [ ! -f "$TEMPLATE_ZIP" ]; then
    echo "❌ 错误: 找不到模板包 $TEMPLATE_ZIP"
    echo "💡 请先运行 ./scripts/package-coder-template.sh"
    exit 1
fi

echo "📁 模板包位置: $TEMPLATE_ZIP"
echo "📊 文件大小: $(ls -lh "$TEMPLATE_ZIP" | awk '{print $5}')"

# Check zip contents
echo ""
echo "📋 检查ZIP内容结构..."
CONTENTS=$(unzip -l "$TEMPLATE_ZIP")
echo "$CONTENTS"

# Verify root level structure
echo ""
echo "🔍 验证Coder要求..."

if echo "$CONTENTS" | grep -q "tencentcloud.tf$"; then
    echo "   ✅ tencentcloud.tf 在根级别"
else
    echo "   ❌ tencentcloud.tf 不在根级别"
fi

if echo "$CONTENTS" | grep -q "variables.tf$"; then
    echo "   ✅ variables.tf 在根级别"
else
    echo "   ❌ variables.tf 不在根级别"
fi

# Check for subdirectories (should not exist)
if echo "$CONTENTS" | grep -E "^\s+[0-9]+.*/" | grep -v "Archive:" | grep -q "/"; then
    echo "   ⚠️  警告: ZIP包含子目录，可能导致Coder无法识别"
    echo "$CONTENTS" | grep -E "^\s+[0-9]+.*/" | head -5
else
    echo "   ✅ 无子目录，结构正确"
fi

echo ""
echo "🎯 总结:"
if echo "$CONTENTS" | grep -q "tencentcloud.tf$" && echo "$CONTENTS" | grep -q "variables.tf$" && echo "$CONTENTS" | grep -E "^\s+[0-9]+.*/" | grep -v "Archive:" | grep -q "/"; then
    echo "✅ 模板包符合Coder要求，可以安全上传！"
else
    echo "❌ 模板包不符合Coder要求，请重新打包"
fi
