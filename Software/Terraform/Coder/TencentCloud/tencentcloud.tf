terraform {
  required_providers {
    tencentcloud = {
      source  = "tencentcloudstack/tencentcloud"
      version = "1.82.24"
    }
    coder = {
      source  = "coder/coder"
      version = "2.11.0"
    }
  }
}

# 配置腾讯云 Provider
provider "tencentcloud" {
  secret_id  = var.tencentcloud_cvm_secret_id
  secret_key = var.tencentcloud_cvm_secret_key
  region     = local.region
}

# 配置 Coder Provider
provider "coder" {}

# 配置 Coder Agent - 支持远程开发工具连接
resource "coder_agent" "main" {
  arch                    = "amd64"            # 目标架构
  os                      = "linux"            # 目标操作系统
  startup_script_behavior = "blocking"         # 阻塞模式: 启动脚本必须完成后工作空间才可用, 确保环境完全初始化.
  dir                     = "/data/workspaces" # 用户连接时的默认工作目录, 直接进入 JuiceFS 挂载的共享工作空间.
  connection_timeout      = 300                # Agent 连接超时时间 (秒) 给予充足时间完成 JuiceFS 挂载和环境初始化

  # 配置 Coder Web UI 中显示的基础工具
  display_apps {
    vscode                 = false # 禁用 VSCode
    vscode_insiders        = false # 禁用 VSCode Insiders
    web_terminal           = true  # 启用 Web 终端
    ssh_helper             = true  # 启用 SSH 助手
    port_forwarding_helper = true  # 启用端口转发助手
  }

  # 内联脚本解决方案 - 直接嵌入脚本内容，解决文件传输问题
  startup_script = <<-EOT
    #!/bin/bash
    set -e

    echo "🚀 开始初始化 Coder 工作空间..."

    # 第一阶段: 系统环境配置
    echo "🔧 第一阶段: 配置系统开发环境..."

    # 更新系统包管理器和环境
    echo "📦 更新系统包管理器和环境..."
    export DEBIAN_FRONTEND=noninteractive
    apt-get update -qq
    apt-get upgrade -y -qq

    # 安装基础系统工具
    echo "🛠️ 安装基础系统工具..."
    apt-get install -y -qq \
        ca-certificates \
        gnupg \
        lsb-release

    # 安装开发基础工具
    echo "🔨 安装开发基础工具..."
    apt-get install -y -qq \
        gcc \
        g++ \
        git \
        build-essential \
        pkg-config \
        libssl-dev \
        unzip \
        zip \
        btop \
        tree \
        jq \
        pigz \
        nano

    # 安装网络和调试工具
    echo "🌐 安装网络和调试工具..."
    apt-get install -y -qq \
        net-tools \
        iputils-ping \
        telnet \
        dnsutils \
        tcpdump \
        strace \
        lsof

    # 安装 Rust 开发环境
    echo "🦀 安装 Rust 开发环境..."
    apt-get install -y cpanminus
    cpanm -q --notest FindBin
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    ~/.cargo/bin/cargo install cross
    ~/.cargo/bin/rustup component add rust-analyzer

    # 安装 Node.js 开发环境
    echo "📦 安装 Node.js 开发环境..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
    apt-get install -y nodejs

    # 安装 Python 开发环境
    echo "🐍 安装 Python 开发环境..."
    apt-get install -y -qq \
        python3 \
        python3-pip \
        python3-venv \
        python3-dev

    # 安装 Docker
    echo "🐳 安装 Docker..."
    curl -fsSL https://get.docker.com | sh
    systemctl enable docker
    systemctl start docker

    # 清理包管理器缓存
    echo "🧹 清理系统缓存..."
    apt-get autoremove -y -qq
    apt-get autoclean -qq

    echo "✅ 系统环境配置完成!"

    # 第二阶段: JuiceFS 配置
    echo "💾 第二阶段: 配置 JuiceFS 云服务..."

    # 设置JuiceFS参数为环境变量
    export FILESYSTEM_NAME="${var.juicefs_vol_name}"
    export JUICEFS_TOKEN="${var.juicefs_token}"
    export COS_ACCESS_KEY="${var.tencentcloud_cos_secret_id}"
    export COS_SECRET_KEY="${var.tencentcloud_cos_secret_key}"
    export COS_ENDPOINT="${var.tencentcloud_cos_endpoint}"

    echo "📋 JuiceFS 配置参数:"
    echo "   文件系统名称: $FILESYSTEM_NAME"
    echo "   COS Endpoint: $COS_ENDPOINT"

    # 安装 JuiceFS
    echo "📥 开始安装 JuiceFS..."
    curl -sSL https://juicefs.com/static/juicefs -o /usr/local/bin/juicefs && chmod +x /usr/local/bin/juicefs

    echo "✅ JuiceFS 安装成功"
    /usr/local/bin/juicefs version

    # 配置 JuiceFS 认证
    echo "🔐 配置 JuiceFS 认证..."
    /usr/local/bin/juicefs auth "$FILESYSTEM_NAME" \
        --token "$JUICEFS_TOKEN" \
        --accesskey "$COS_ACCESS_KEY" \
        --secretkey "$COS_SECRET_KEY" \
        --bucket "$COS_ENDPOINT"

    echo "✅ JuiceFS 认证成功"

    # 创建挂载点
    echo "📁 创建工作空间挂载点..."
    mkdir -p /data && mkdir -p /data/workspaces

    # 挂载 JuiceFS 文件系统
    echo "🔗 挂载 JuiceFS 文件系统..."
    /usr/local/bin/juicefs mount "$FILESYSTEM_NAME" /data/workspaces \
        --update-fstab \
        --cache-size=10240

    # 等待挂载完成
    sleep 5

    # 验证挂载状态
    if mountpoint -q /data/workspaces; then
        echo "✅ JuiceFS 文件系统挂载成功"
        echo "📂 挂载点: /data/workspaces"
    else
        echo "❌ JuiceFS 文件系统挂载失败"
        exit 1
    fi

    # 第三阶段: 创建软链接
    echo "🔗 第三阶段: 创建软链接..."
    if ln -sf /data/workspaces /root/workspace; then
        echo "✅ 软链接创建成功"
    else
        echo "❌ 软链接创建失败"
        exit 1
    fi

    # 打印初始化完成信息
    echo ""
    echo "🎉 Coder 工作空间初始化完成!"
    echo "🚀 开发环境已就绪, 开始您的编程之旅吧!"
  EOT

  # 环境变量配置
  env = {
    WORKSPACE_DIR = "/data/workspaces"
    JUICEFS_MOUNT = "/data/workspaces"
  }

  # 元数据配置
  metadata {
    display_name = "JuiceFS"
    key          = "juicefs_status"
    script       = "mountpoint -q /data/workspaces && echo 'Ready' || echo 'Not ready'"
    interval     = 10
    timeout      = 1
    order        = 1
  }
  metadata {
    display_name = "CPU"
    key          = "cpu_usage"
    script       = "coder stat cpu"
    interval     = 10
    timeout      = 1
    order        = 2
  }
  metadata {
    display_name = "RAM"
    key          = "ram_usage"
    script       = "coder stat mem"
    interval     = 10
    timeout      = 1
    order        = 3
  }
  metadata {
    display_name = "System Disk"
    key          = "system_disk_usage"
    script       = "coder stat disk"
    interval     = 10
    timeout      = 1
    order        = 4
  }
  metadata {
    display_name = "JuiceFS Disk"
    key          = "juicefs_disk_usage"
    script       = "coder stat disk --path /data/workspaces"
    interval     = 10
    timeout      = 1
    order        = 5
  }
}

# IP 地址元数据显示
resource "coder_metadata" "instance_ips" {
  count       = data.coder_workspace.me.start_count
  resource_id = tencentcloud_instance.coder_instance.id

  item {
    key   = "Public IP"
    value = tencentcloud_instance.coder_instance.public_ip
  }

  item {
    key   = "Private IP"
    value = tencentcloud_instance.coder_instance.private_ip
  }
}

# ==================== JetBrains Gateway 模块配置 ====================
#
# 配置 JetBrains Gateway 模块，支持多种 IDE 的远程开发
# 参考文档: https://coder.com/docs/admin/templates/extending-templates/jetbrains-preinstall
#
module "jetbrains_gateway" {
  count          = data.coder_workspace.me.start_count
  source         = "registry.coder.com/modules/jetbrains-gateway/coder"
  version        = "1.2.2"
  agent_id       = coder_agent.main.id
  folder         = "/data/workspaces"
  jetbrains_ides = ["RR", "WS", "PY", "CL"]   # RustRover, WebStorm, PyCharm, CLion
  default        = "RR"                       # 默认使用 RustRover
  latest         = true                       # 使用最新版本
}

# ==================== 启动腾讯云 CVM 实例 ====================

# 创建 CVM 实例
resource "tencentcloud_instance" "coder_instance" {
  # 基本配置
  instance_name     = "coder-${data.coder_workspace_owner.me.name}-${data.coder_workspace.me.name}"
  availability_zone = local.availability_zone
  image_id          = local.image_id
  instance_type     = data.coder_parameter.instance_type.value
  hostname          = "coder-${substr(data.coder_workspace.me.id, 0, 8)}"
  project_id        = local.project_id

  # 计费配置
  instance_charge_type = data.coder_parameter.instance_charge_type.value

  # 竞价实例配置 (选择竞价实例时使用用户自定义价格)
  spot_instance_type = data.coder_parameter.instance_charge_type.value == "SPOTPAID" ? "ONE-TIME" : null
  spot_max_price     = data.coder_parameter.instance_charge_type.value == "SPOTPAID" ? data.coder_parameter.spot_max_price.value : null

  # 网络配置 (使用固定配置)
  vpc_id                     = local.vpc_id
  subnet_id                  = local.subnet_id
  orderly_security_groups    = [local.security_group_id]
  allocate_public_ip         = local.allocate_public_ip
  internet_charge_type       = local.internet_charge_type
  internet_max_bandwidth_out = local.internet_max_bandwidth_out

  # 存储配置 (使用固定配置)
  system_disk_type = local.system_disk_type
  system_disk_size = local.system_disk_size

  # SSH 密钥认证 (使用固定配置)
  key_ids = [local.ssh_key_id]

  # 用户数据脚本 - 安装 Coder Agent (内联版本)
  user_data = base64encode(<<-EOF
    #!/bin/bash
    set -e

    # Coder Agent 安装和启动脚本
    echo "🚀 开始安装 Coder Agent..."

    # 安装 Coder Agent 基础依赖
    echo "📦 安装 Coder Agent 基础依赖..."
    export DEBIAN_FRONTEND=noninteractive
    apt-get update -qq
    apt-get install -y -qq \
        curl \
        wget \
        systemd \
        dnsutils

    # 直接以 root 用户安装和运行 Coder Agent
    cd /root

    # 解码并执行 Coder Agent 初始化脚本
    echo "${base64encode(coder_agent.main.init_script)}" | base64 -d > /tmp/coder_init.sh
    chmod +x /tmp/coder_init.sh

    # 设置 Coder Agent Token 环境变量
    export CODER_AGENT_TOKEN="${coder_agent.main.token}"

    # 执行初始化脚本
    /tmp/coder_init.sh

    # 清理临时文件
    rm -f /tmp/coder_init.sh

    echo "✅ Coder Agent 安装完成"
    echo "💡 系统环境和 JuiceFS 将在 Agent 启动后配置"
  EOF
  )

  # 服务配置 (按要求禁用相关服务)
  disable_security_service   = true # 不启用安全加固
  disable_monitor_service    = true # 不启用云监控
  disable_automation_service = true # 不启用自动化助手

  # 标签配置
  tags = {
    "Coder"        = "true"
    "Owner"        = data.coder_workspace_owner.me.name
    "Workspace"    = data.coder_workspace.me.name
    "InstanceType" = data.coder_parameter.instance_type.value
    "ChargeType"   = data.coder_parameter.instance_charge_type.value
    "Environment"  = "Development"
    "CreatedBy"    = "Terraform"
  }
}